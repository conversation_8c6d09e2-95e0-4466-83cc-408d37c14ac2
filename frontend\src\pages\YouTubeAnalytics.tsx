import React, { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  InputAdornment,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  CardActions,
  Chip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Snackbar,
  Alert,
  Link,
  Tooltip,
  IconButton,
  SelectChangeEvent,
  ButtonGroup,
} from "@mui/material";
import {
  YouTube as YouTubeIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  FilterList as FilterIcon,
  OpenInNew as OpenInNewIcon,
  ContentCopy as CopyIcon,
  Save as SaveIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  ErrorOutline as ErrorIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { api, hasValidToken } from "../services/api";
import { useNavigate } from "react-router-dom";
import { trackSearchActivity } from "../services/activityService";
import { saveVideoToLibrary } from "../services/youtubeService";

// Define interfaces for our data
// Interface for the raw result from the backend (keyed by video ID)
interface AnalysisResults {
  [videoId: string]: AnalyzedVideoData;
}

// Interface for the outlier score object within the result
interface OutlierScoreData {
  Final_Outlier_Score: number;
  Performance_Category: string;
  Time_Adjusted_Multiple: number | null;
  p_channel: number | null;
  avg_views_at_30_days: number | null;
  error: string | null;
}

// Interface for a single video's data, including the nested outlier score
interface AnalyzedVideoData {
  youtube_id: string;
  title: string;
  channel_title: string;
  views: number;
  likes: number;
  comments: number;
  channel_subscribers: number;
  publish_date: string;
  channel_id?: string;
  description?: string;
  // Add the nested outlier score object
  outlier_score: OutlierScoreData;
  // Keep old fields for potential backward compatibility or other uses, but mark optional
  glow_ai_score?: number; // Legacy
  view_multiple?: number; // Legacy
  performance_category?: string; // Legacy
  time_adjusted_avg_views?: number; // Legacy
  engagement_rate?: number; // Legacy
  view_sub_ratio?: number; // Legacy
  error?: string; // Top-level error for fetch failures
}

// Define sort configuration type
type SortDirection = "asc" | "desc";
type SortKey = "score" | "views" | "date" | "multiple";

interface SortConfig {
  key: SortKey;
  direction: SortDirection;
}

const YouTubeAnalytics: React.FC = () => {
  const navigate = useNavigate();

  // Check authentication on component mount
  useEffect(() => {
    const checkAuth = async () => {
      const isValid = await hasValidToken();
      if (!isValid) {
        navigate("/login");
      }
    };
    checkAuth();
  }, [navigate]);

  // State for search
  const [searchInput, setSearchInput] = useState<string>("");
  const [searching, setSearching] = useState<boolean>(false);
  const [analyzing, setAnalyzing] = useState<boolean>(false);

  // State for videos
  const [videoIds, setVideoIds] = useState<string[]>([]);
  // NEW: State for raw, unfiltered data from API
  const [rawAnalyzedData, setRawAnalyzedData] = useState<AnalysisResults>({});
  // OLD: analyzedVideos is now derived state for display
  const [displayedVideos, setDisplayedVideos] = useState<AnalyzedVideoData[]>(
    []
  );

  // State for filters
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [scoreRange, setScoreRange] = useState<number[]>([0, 100]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // NEW: State for sorting
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: "score",
    direction: "desc",
  });

  // State for notifications
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info" | "warning";
  }>({ open: false, message: "", severity: "info" });

  // Performance categories for filtering (using new categories)
  const performanceCategories = [
    "Unicorn Outlier",
    "Exceptional Outlier",
    "Strong Outlier",
    "Good Performance / Mild Outlier",
    "Average / Slightly Below Average",
    "Underperforming",
    "Extremely Underperforming",
    "Calculation Pending", // Added for potential initial state
    "Video too young", // Added for error state
    "Error", // Generic error
  ];

  // Function to get color based on the NEW score
  const getScoreColor = (score: number | undefined | null): string => {
    if (typeof score !== "number") return "#9E9E9E"; // Grey for invalid/missing score
    if (score >= 91) return "#8E44AD"; // Unicorn (purple)
    if (score >= 81) return "#2ECC71"; // Exceptional (green)
    if (score >= 71) return "#27AE60"; // Strong (darker green)
    if (score >= 51) return "#3498DB"; // Good/Mild (blue)
    if (score >= 31) return "#1ABC9C"; // Average (teal)
    if (score >= 11) return "#E67E22"; // Underperforming (orange)
    return "#C0392B"; // Extremely Underperforming (red)
  };

  // Function to handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  // Function to handle search
  const handleSearch = useCallback(async () => {
    if (!searchInput.trim()) {
      setNotification({
        open: true,
        message: "Please enter a search term",
        severity: "warning",
      });
      return;
    }
    const isTokenValid = await hasValidToken();
    if (!isTokenValid) {
      setNotification({
        open: true,
        message: "Please log in to continue",
        severity: "error",
      });
      navigate("/login");
      return;
    }

    setSearching(true);
    setVideoIds([]);
    setRawAnalyzedData({}); // Clear raw data
    setDisplayedVideos([]); // Clear displayed data

    try {
      // Removed search request log
      const response = await api.post("/api/youtube/search", {
        query: searchInput.trim(),
        max_results: 50, // Updated to 50
      });
      // Removed search response log

      if (
        response.data.results &&
        Array.isArray(response.data.results) &&
        response.data.results.length > 0
      ) {
        const newVideoIds = response.data.results.map((v: any) => v.video_id);
        setVideoIds(newVideoIds); // Keep track of searched IDs if needed later
        setNotification({
          open: true,
          message: `Found ${response.data.results.length} videos. Analyzing...`,
          severity: "info",
        });
        trackSearchActivity(Date.now().toString(), searchInput.trim()).catch(
          console.error
        );
        await analyzeVideos(newVideoIds); // Analyze the found videos
      } else {
        setNotification({
          open: true,
          message: "No videos found for your search query",
          severity: "info",
        });
      }
    } catch (error: any) {
      console.error("Error searching YouTube:", error);
      setNotification({
        open: true,
        message: error.response?.data?.error || "Failed to search YouTube",
        severity: "error",
      });
    } finally {
      setSearching(false);
    }
  }, [searchInput, navigate]);

  // Function to analyze videos - NOW fetches ALL data, stores raw results
  const analyzeVideos = useCallback(async (ids: string[]) => {
    if (!ids.length) return;
    setAnalyzing(true);
    try {
      // Removed analyze request log
      const response = await api.post("/api/youtube/analyze/outliers", {
        video_ids: ids,
        // REMOVED filter params: min_score, max_score, performance_category
      });
      // Removed analyze response log

      if (response.data && response.data.results) {
        const resultsObj = response.data.results as AnalysisResults;
        setRawAnalyzedData(resultsObj); // Store the raw object keyed by ID
        setNotification({
          open: true,
          message: `Successfully analyzed ${
            Object.keys(resultsObj).length
          } videos`,
          severity: "success",
        });
      } else {
        console.error("Invalid response format:", response.data);
        setRawAnalyzedData({});
        setNotification({
          open: true,
          message: "Analysis returned invalid data format",
          severity: "warning",
        });
      }
    } catch (err: any) {
      console.error("Error analyzing videos:", err);
      setRawAnalyzedData({});
      setNotification({
        open: true,
        message: "Error analyzing videos. Please try again.",
        severity: "error",
      });
    } finally {
      setAnalyzing(false);
    }
  }, []); // Removed dependencies on filters

  // NEW: useEffect for client-side filtering and sorting
  useEffect(() => {
    const videoArray = Object.values(rawAnalyzedData); // Convert raw data object to array

    const filtered = videoArray.filter((video) => {
      // Handle potential top-level fetch errors first
      if (video.error) return false;
      // Handle score calculation errors
      if (
        video.outlier_score?.error &&
        video.outlier_score.error !== "Video too young"
      ) {
        // Optionally keep videos with calculation errors, or filter them out
        // return true; // Keep errors
        return false; // Filter out calculation errors (except 'too young')
      }

      const score = video.outlier_score?.Final_Outlier_Score;
      const category = video.outlier_score?.Performance_Category;

      // Apply score range filter (only if score is a valid number)
      const scoreFilterPassed =
        typeof score === "number"
          ? score >= scoreRange[0] && score <= scoreRange[1]
          : true; // Pass if score is not calculable yet or error

      // Apply category filter (only if category exists and filter is set)
      const categoryFilterPassed =
        !selectedCategory || (category && category === selectedCategory);

      return scoreFilterPassed && categoryFilterPassed;
    });

    const sorted = [...filtered].sort((a, b) => {
      const aScoreData = a.outlier_score;
      const bScoreData = b.outlier_score;
      let compareA: any;
      let compareB: any;

      switch (sortConfig.key) {
        case "score":
          compareA =
            typeof aScoreData?.Final_Outlier_Score === "number"
              ? aScoreData.Final_Outlier_Score
              : -1; // Treat errors/nulls as lowest score
          compareB =
            typeof bScoreData?.Final_Outlier_Score === "number"
              ? bScoreData.Final_Outlier_Score
              : -1;
          break;
        case "views":
          compareA = a.views ?? 0;
          compareB = b.views ?? 0;
          break;
        case "date":
          compareA = a.publish_date ? new Date(a.publish_date).getTime() : 0;
          compareB = b.publish_date ? new Date(b.publish_date).getTime() : 0;
          break;
        case "multiple":
          compareA =
            typeof aScoreData?.Time_Adjusted_Multiple === "number"
              ? aScoreData.Time_Adjusted_Multiple
              : -1; // Treat nulls as lowest
          compareB =
            typeof bScoreData?.Time_Adjusted_Multiple === "number"
              ? bScoreData.Time_Adjusted_Multiple
              : -1;
          break;
        default:
          return 0;
      }

      if (compareA < compareB) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (compareA > compareB) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });

    setDisplayedVideos(sorted);
  }, [rawAnalyzedData, scoreRange, selectedCategory, sortConfig]);

  // REMOVED: applyFilters and resetFilters no longer call API
  const applyFilters = () => {
    // Now just triggers the useEffect by potentially changing filter state (already done by UI handlers)
    // Removed client-side filter log
  };

  const resetFilters = () => {
    setScoreRange([0, 100]);
    setSelectedCategory("");
    // Also reset sort? Optional.
    // setSortConfig({ key: 'score', direction: 'desc' });
    // Removed filter reset log
  };

  // Function to handle notification close
  const handleNotificationClose = () => {
    setNotification({ ...notification, open: false });
  };

  // Function to copy video ID to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setNotification({
      open: true,
      message: "Copied to clipboard",
      severity: "success",
    });
  };

  // Function to format large numbers
  const formatNumber = (num: number | undefined | null): string => {
    if (typeof num !== "number") return "-";
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  // Function to format date
  const formatDate = (dateString: string | undefined | null): string => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch (e) {
      return "Invalid Date";
    }
  };

  // Function to save video to library
  const saveToLibrary = async (videoId: string) => {
    const isTokenValid = await hasValidToken();
    if (!isTokenValid) {
      setNotification({
        open: true,
        message: "Please log in to continue",
        severity: "error",
      });
      navigate("/login");
      return;
    }
    try {
      // Check if video data exists in raw data
      const videoData = rawAnalyzedData[videoId];
      if (!videoData) {
        setNotification({
          open: true,
          message: "Video data not found. Please try searching again.",
          severity: "error",
        });
        return;
      }
      // Fetch details first to ensure DB entry exists (or create it)
      await api.get(`/youtube/video/${videoId}`);

      // Call the new service function
      await saveVideoToLibrary(videoId);

      setNotification({
        open: true,
        message: "Video data saved to content library successfully",
        severity: "success",
      });
    } catch (err: any) {
      console.error("Error saving to library:", err);
      let errorMessage =
        err.response?.data?.error || err.message || "Error saving to library";
      if (
        err.response?.status === 404 ||
        errorMessage.includes("fetch the video first")
      ) {
        errorMessage =
          "Video data not found in the database. Please try searching again.";
      }
      setNotification({ open: true, message: errorMessage, severity: "error" });
    }
  };

  // NEW: Handler for changing sort
  const handleSortChange = (key: SortKey) => {
    setSortConfig((prevConfig) => {
      const direction =
        prevConfig.key === key && prevConfig.direction === "desc"
          ? "asc"
          : "desc";
      return { key, direction };
    });
  };

  // Helper to render sort icon
  const renderSortIcon = (key: SortKey) => {
    if (sortConfig.key !== key) return null;
    return sortConfig.direction === "desc" ? (
      <ArrowDownwardIcon fontSize="small" />
    ) : (
      <ArrowUpwardIcon fontSize="small" />
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 4,
          }}
        >
          <div>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              YouTube Search & Analyze
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Search videos and analyze performance outliers
            </Typography>
          </div>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<FilterIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ borderRadius: 2 }}
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </Box>
      </motion.div>

      {/* Search Bar */}
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Enter YouTube URL, video ID, or search term"
          variant="outlined"
          value={searchInput}
          onChange={handleSearchInputChange}
          disabled={searching || analyzing}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<YouTubeIcon />}
                  onClick={handleSearch}
                  disabled={!searchInput.trim() || searching || analyzing}
                  sx={{ borderRadius: 2 }}
                >
                  {searching ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    "Search"
                  )}
                </Button>
              </InputAdornment>
            ),
            sx: { borderRadius: 2, pr: 1 },
          }}
          onKeyPress={(e) => {
            if (e.key === "Enter") {
              handleSearch();
            }
          }}
        />

        {/* Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
          >
            <Box
              sx={{
                mt: 3,
                p: 2,
                bgcolor: "background.default",
                borderRadius: 2,
              }}
            >
              <Typography variant="h6" gutterBottom>
                Filter Results
              </Typography>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={5}>
                  <Typography gutterBottom>
                    Outlier Score Range: {scoreRange[0]} - {scoreRange[1]}
                  </Typography>
                  <Slider
                    value={scoreRange}
                    onChange={(_, newValue) =>
                      setScoreRange(newValue as number[])
                    }
                    valueLabelDisplay="auto"
                    min={0}
                    max={100}
                    sx={{
                      "& .MuiSlider-thumb": {
                        color: getScoreColor(scoreRange[1]),
                      },
                      "& .MuiSlider-track": {
                        color: getScoreColor(scoreRange[1]),
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={5}>
                  <FormControl fullWidth>
                    <InputLabel>Performance Category</InputLabel>
                    <Select
                      value={selectedCategory}
                      label="Performance Category"
                      onChange={(e: SelectChangeEvent) =>
                        setSelectedCategory(e.target.value)
                      }
                    >
                      <MenuItem value="">All Categories</MenuItem>
                      {/* Use new categories */}
                      {performanceCategories.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid
                  item
                  xs={12}
                  md={2}
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  <Button
                    variant="outlined"
                    onClick={resetFilters}
                    disabled={analyzing}
                  >
                    Reset
                  </Button>
                  {/* Apply button removed - filtering is now automatic via useEffect */}
                </Grid>
              </Grid>
            </Box>
          </motion.div>
        )}
      </Paper>

      {/* Results */}
      {analyzing ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            my: 4,
          }}
        >
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Analyzing videos...
          </Typography>
        </Box>
      ) : displayedVideos && displayedVideos.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography variant="h5">
              Analysis Results ({displayedVideos.length})
            </Typography>
            {/* NEW: Sorting Controls */}
            <ButtonGroup variant="outlined" size="small">
              <Button
                onClick={() => handleSortChange("score")}
                endIcon={renderSortIcon("score")}
              >
                Score
              </Button>
              <Button
                onClick={() => handleSortChange("views")}
                endIcon={renderSortIcon("views")}
              >
                Views
              </Button>
              <Button
                onClick={() => handleSortChange("date")}
                endIcon={renderSortIcon("date")}
              >
                Date
              </Button>
              <Button
                onClick={() => handleSortChange("multiple")}
                endIcon={renderSortIcon("multiple")}
              >
                Multiple
              </Button>
            </ButtonGroup>
          </Box>

          <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Video</TableCell>
                  <TableCell align="center">Views</TableCell>
                  <TableCell align="center">Multiple</TableCell>
                  <TableCell align="center">Outlier Score</TableCell>
                  <TableCell align="center">Performance</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {displayedVideos.map((video) => {
                  const scoreData = video.outlier_score;
                  const hasError = !!scoreData?.error;
                  const score = scoreData?.Final_Outlier_Score;
                  const category = scoreData?.Performance_Category ?? "N/A";
                  const multiple = scoreData?.Time_Adjusted_Multiple;

                  return (
                    <TableRow key={video.youtube_id}>
                      <TableCell>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <img
                            src={`https://i.ytimg.com/vi/${video.youtube_id}/mqdefault.jpg`}
                            alt={video.title}
                            style={{
                              width: 120,
                              height: 68,
                              borderRadius: 4,
                              marginRight: 12,
                            }}
                          />
                          <Box>
                            <Typography
                              variant="body1"
                              fontWeight="bold"
                              sx={{ mb: 0.5 }}
                            >
                              {video.title.length > 60
                                ? video.title.substring(0, 60) + "..."
                                : video.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {video.channel_title}
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              {formatDate(video.publish_date)}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="body1" fontWeight="bold">
                          {formatNumber(video.views)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Subs: {formatNumber(video.channel_subscribers)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        {/* FIX: Check if multiple is a number before toFixed */}
                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          color={
                            typeof multiple === "number" && multiple >= 2
                              ? "success.main"
                              : "text.primary"
                          }
                        >
                          {typeof multiple === "number"
                            ? `${multiple.toFixed(1)}x`
                            : "-"}
                        </Typography>
                        {/* Removed avg views display for simplicity */}
                      </TableCell>
                      <TableCell align="center">
                        {/* FIX: Use new score and handle errors */}
                        {hasError ? (
                          <Tooltip title={scoreData.error}>
                            <ErrorIcon color="error" />
                          </Tooltip>
                        ) : (
                          <Box
                            sx={{ display: "flex", justifyContent: "center" }}
                          >
                            <Box
                              sx={{
                                width: 60,
                                height: 60,
                                borderRadius: "50%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                bgcolor: getScoreColor(score),
                                color: "white",
                                fontWeight: "bold",
                                fontSize: "1.2rem",
                              }}
                            >
                              {typeof score === "number"
                                ? Math.round(score)
                                : "-"}
                            </Box>
                          </Box>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {/* FIX: Use new category and handle errors */}
                        <Chip
                          label={hasError ? "Error" : category}
                          sx={{
                            bgcolor: hasError
                              ? "error.main"
                              : getScoreColor(score),
                            color: "white",
                            fontWeight: "bold",
                          }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <CardActions sx={{ justifyContent: "flex-end", p: 0 }}>
                          <Tooltip title="Save to Content Library">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => saveToLibrary(video.youtube_id)}
                            >
                              <SaveIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Copy Video ID">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => copyToClipboard(video.youtube_id)}
                            >
                              <CopyIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Open in YouTube">
                            <IconButton
                              size="small"
                              color="error"
                              component="a"
                              href={`https://youtube.com/watch?v=${video.youtube_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <YouTubeIcon />
                            </IconButton>
                          </Tooltip>
                        </CardActions>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </motion.div>
      ) : !searching && !analyzing && videoIds.length > 0 ? ( // Show message only if search was done but results are empty after filtering
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No videos match your current filter criteria
          </Typography>
        </Box>
      ) : (
        // Initial state before search
        <Paper sx={{ p: 4, borderRadius: 2, textAlign: "center" }}>
          <YouTubeIcon sx={{ fontSize: 60, color: "error.main", mb: 2 }} />
          <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
            Search & Analyze YouTube Videos
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Enter a YouTube URL, video ID, or search term to analyze videos
            using the outlier score algorithm.
          </Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 1,
              flexWrap: "wrap",
            }}
          >
            {/* Updated legend */}
            <Chip
              label="Unicorn (91+)"
              sx={{
                bgcolor: getScoreColor(95),
                color: "white",
                fontWeight: "bold",
              }}
            />
            <Chip
              label="Exceptional (81-90)"
              sx={{
                bgcolor: getScoreColor(85),
                color: "white",
                fontWeight: "bold",
              }}
            />
            <Chip
              label="Strong (71-80)"
              sx={{
                bgcolor: getScoreColor(75),
                color: "white",
                fontWeight: "bold",
              }}
            />
            <Chip
              label="Good (51-70)"
              sx={{
                bgcolor: getScoreColor(65),
                color: "white",
                fontWeight: "bold",
              }}
            />
            <Chip
              label="Average (31-50)"
              sx={{
                bgcolor: getScoreColor(45),
                color: "white",
                fontWeight: "bold",
              }}
            />
            <Chip
              label="Underperforming (0-30)"
              sx={{
                bgcolor: getScoreColor(25),
                color: "white",
                fontWeight: "bold",
              }}
            />
          </Box>
        </Paper>
      )}

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleNotificationClose}
          severity={notification.severity}
          variant="filled"
          sx={{ width: "100%" }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default YouTubeAnalytics;
