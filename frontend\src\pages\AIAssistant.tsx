import React, { useState, useEffect, useRef, use<PERSON><PERSON>back, memo } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  CircularProgress,
  Divider,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Card,
  CardContent,
  Menu,
  MenuItem,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Checkbox,
  Snackbar,
  Alert,
  InputAdornment,
  ListItemButton,
  ListItemIcon,
  Switch,
  FormControlLabel,
  Drawer,
} from "@mui/material";
import {
  Chat as ChatIcon,
  Send as SendIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Info as InfoIcon,
  LibraryBooks as LibraryBooksIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  BookmarkAdd as BookmarkAddIcon,
  Cancel as CancelIcon,
  ContentCopy as ContentCopyIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import {
  api,
  saveContentItem,
  SaveContentPayload,
} from "../services/api";
import { useNavigate } from "react-router-dom";
import { debounce } from "../utils/errorHandlers";
import YouTubeIcon from "@mui/icons-material/YouTube";
import ArticleIcon from "@mui/icons-material/Article";
import SaveToLibraryDialog from "../components/dialogs/SaveToLibraryDialog";
import MarkdownRenderer from "../components/MarkdownRenderer";
import StructuredJsonRenderer from "../components/StructuredJsonRenderer";
import ScriptFeedbackWidget from "../components/ScriptFeedbackWidget";
import AuthErrorBoundary from "../components/AuthErrorBoundary";
import { useAuthenticatedApi } from "../hooks/useAuthenticatedApi";
import { useAuth } from "../contexts/AuthContext";
import { trackChatActivity } from "../services/activityService";
import { stripFences } from "../utils/stripFences";
import "../styles/markdown.css";

import {
  useContentSelection as useContentSelectionOriginal,
  ContentSelectionProvider,
} from "../contexts/ContentSelectionContext";
import { useBusinessContextSelection } from "../contexts/BusinessContextSelectionContext";

const useContentSelection = () => {
  try {
    return useContentSelectionOriginal();
  } catch (error) {
    return {
      selectedContentIds: [] as string[],
      toggleContentSelection: (_id: string) => {},
      clearContentSelection: () => {},
      isContentSelected: (_id: string) => false,
    };
  }
};

interface ChatSession {
  id: string;
  title: string;
  created_at: string;
  session_type?: string;
}

interface ChatMessage {
  id: string;
  session_id: string;
  role: "user" | "assistant";
  content: string;
  created_at: string;
  isLoading?: boolean;
  metadata?: {
    action_metadata?: {
      action: string;
      context_id?: string;
      payload?: any;
    };
  };
}

interface BusinessContext {
  id: string;
  offer_description: string;
  target_audience: string;
  brand_voice: string;
  key_benefits: string;
  unique_value_proposition: string;
}

interface ContentItem {
  id: string;
  title: string;
  content: string;
  content_type: string | null;
  tags: string[] | string | null;
  created_at: string;
  updated_at: string;
  thumbnail_url?: string;
}

const ChatMessageItem = memo(
  ({
    message,
    formatTimestamp,
    onSaveMessageClick,
    setNotification,
  }: {
    onSaveMessageClick: (message: ChatMessage) => void;
    message: ChatMessage;
    formatTimestamp: (timestamp: string) => string;
    setNotification: (notification: {
      open: boolean;
      message: string;
      severity: "success" | "info" | "warning" | "error";
    }) => void;
  }) => {


    const isPotentialScript =
      message.role === "assistant" && !message.isLoading;

    const handleCopyToClipboard = async (textToCopy: string) => {
      try {
        await navigator.clipboard.writeText(stripFences(textToCopy));
        setNotification({
          open: true,
          message: "Script copied to clipboard!",
          severity: "success",
        });
      } catch (err) {
        console.error("Failed to copy text: ", err);
        setNotification({
          open: true,
          message: "Failed to copy script.",
          severity: "error",
        });
      }
    };

    let renderAsStructured = false;
    let parsedData = null;

    if (isPotentialScript && !message.isLoading) {
      try {
        const cleaned = stripFences(message.content);
        if (cleaned.startsWith("{") || cleaned.startsWith("[")) {
          parsedData = JSON.parse(cleaned);

          // Check if it's a structured script
          if (parsedData && (parsedData.hook || parsedData.outline || parsedData.cta)) {
            renderAsStructured = true;
          }
        }
      } catch (e) {
        // Ignore parsing errors here, renderers handle fallback
      }
    }

    // Optimization feature removed

    return (
      <Box
        data-testid="chat-message"
        data-role={message.role}
        data-message-id={message.id}
        sx={{
          display: "flex",
          flexDirection: message.role === "user" ? "row-reverse" : "row",
          alignItems: "flex-start",
          gap: 1,
          mb: 2,
        }}
      >
        <Avatar
          sx={{
            bgcolor: message.role === "user" ? "primary.main" : "success.main",
            width: 28,
            height: 28,
            mt: 0.5,
            "& .MuiSvgIcon-root": {
              color: "#fff",
            },
          }}
        >
          {message.role === "user" ? (
            <PersonIcon sx={{ fontSize: 16 }} />
          ) : (
            <BotIcon sx={{ fontSize: 16 }} />
          )}
        </Avatar>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            maxWidth: "85%",
            width: "100%",
          }}
        >
          <Card
            sx={{
              position: "relative",
              borderRadius: 1,
              boxShadow: 0,
              bgcolor:
                message.role === "user"
                  ? "primary.main"
                  : (theme) =>
                      theme.palette.mode === "dark"
                        ? "background.paper"
                        : theme.palette.grey[100],
              color:
                message.role === "user"
                  ? "primary.contrastText"
                  : "text.primary",
            }}
          >
            {/* Apply button removed */}

            <CardContent
              sx={{
                pt: 1.5,
                pb: 1.5,
                px: 1.5,
                position: "relative",
                wordBreak: "break-word",
                whiteSpace: "pre-wrap",
              }}
            >
              {message.isLoading ? (
                <CircularProgress size={20} />
              ) : renderAsStructured ? (
                <StructuredJsonRenderer text={message.content} />
              ) : (
                <MarkdownRenderer content={message.content} />
              )}
            </CardContent>

            {/* Bottom action bar: Feedback widget on left, Copy+Save on right */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                px: 1.5,
                pb: 1,
              }}
            >
              {/* Show feedback widget on all non-loading messages */}
              {!message.isLoading && (
                <ScriptFeedbackWidget
                  sessionId={message.session_id}
                  messageId={message.id}
                  setNotification={setNotification}
                />
              )}

              {/* Copy & Save buttons side by side */}
              {message.role === "assistant" && !message.isLoading && (
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Tooltip title="Copy text">
                    <IconButton
                      size="small"
                      onClick={() => handleCopyToClipboard(message.content)}
                      aria-label="Copy message text"
                    >
                      <ContentCopyIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Save to Content Library">
                    <IconButton
                      size="small"
                      onClick={() => onSaveMessageClick(message)}
                      aria-label="Save message to library"
                    >
                      <BookmarkAddIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
            </Box>
          </Card>
          <Typography
            variant="caption"
            sx={{
              alignSelf: message.role === "user" ? "flex-end" : "flex-start",
              color: "text.secondary",
              mt: 0.5,
            }}
          >
            {formatTimestamp(message.created_at)}
          </Typography>
        </Box>
      </Box>
    );
  }
);

const AIAssistant: React.FC = () => {
  // Get auth session for API calls
  const { session, signOut } = useAuth();

  // Utility function to safely extract string from error objects
  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };

  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(
    null
  );
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [sessionLoading, setSessionLoading] = useState<boolean>(true);

  const [messageInput, setMessageInput] = useState<string>("");
  const [sending, setSending] = useState<boolean>(false);

  const [businessContexts, setBusinessContexts] = useState<BusinessContext[]>(
    []
  );
  const [includeBusinessContext, setIncludeBusinessContext] =
    useState<boolean>(false);

  const [contentItems, setContentItems] = useState<ContentItem[]>([]);
  const [contentLoading, setContentLoading] = useState<boolean>(false);
  const [knowledgeDrawerOpen, setKnowledgeDrawerOpen] =
    useState<boolean>(false);

  const [activeKnowledgeSources, setActiveKnowledgeSources] = useState<{
    businessContext: boolean;
    contentItems: boolean;
  }>({
    businessContext: false,
    contentItems: false,
  });

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [sessionMenuOpen, setSessionMenuOpen] = useState<boolean>(false);

  const [selectedSessions, setSelectedSessions] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState<boolean>(false);

  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "info" | "warning" | "error";
  }>({
    open: false,
    message: "",
    severity: "info",
  });



  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState<boolean>(false);
  const [messageToSave, setMessageToSave] = useState<ChatMessage | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  const {
    selectedContentIds,
    toggleContentSelection,
    clearContentSelection,
    isContentSelected,
  } = useContentSelection();
  const { selectedBusinessContextId, setSelectedBusinessContextId } =
    useBusinessContextSelection();

  const navigate = useNavigate();
  const isInitialMount = useRef(true);

  // Ref to control streaming
  const streamControllerRef = useRef<AbortController | null>(null);



  const debouncedScrollToBottom = useCallback(
    debounce(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, 100),
    []
  );

  useEffect(() => {
    debouncedScrollToBottom();
  }, [messages, debouncedScrollToBottom]);

  useEffect(() => {
    if (!messageContainerRef.current) return;

    const handleResize = debounce((entries: ResizeObserverEntry[]) => {
      debouncedScrollToBottom();
    }, 100);

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(messageContainerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [debouncedScrollToBottom]);

  const scrollToBottom = () => {
    debouncedScrollToBottom();
  };

  // Use authenticated API hooks for better error handling
  // Use a single component ID to coordinate rate limiting across all API calls
  const chatSessionsApi = useAuthenticatedApi<ChatSession[]>('AIAssistant');
  const businessContextsApi = useAuthenticatedApi<BusinessContext[]>('AIAssistant');
  const contentItemsApi = useAuthenticatedApi<{content_items: ContentItem[]}>('AIAssistant');

  const fetchChatSessions = async () => {
    setSessionLoading(true);
    try {
      console.log("[DEBUG] Fetching chat sessions from /chat/sessions");
      const data = await chatSessionsApi.execute("/chat/sessions", {
        retryCount: 1,
        retryDelay: 2000,
      });

      setChatSessions(data);

      if (data && data.length > 0) {
        const mostRecent = data[0];
        setCurrentSession(mostRecent);
        fetchMessages(mostRecent.id);
      }
    } catch (err: any) {
      console.error("Error fetching chat sessions:", err);
      const errorMessage = getErrorMessage(err);

      // Don't show auth errors as notifications since they're handled by the error boundary
      if (!errorMessage.toLowerCase().includes('authentication') &&
          !errorMessage.toLowerCase().includes('unauthorized')) {
        setNotification({
          open: true,
          message: errorMessage,
          severity: "error",
        });
      }
    } finally {
      setSessionLoading(false);
    }
  };

  const fetchBusinessContexts = async () => {
    try {
      const data = await businessContextsApi.execute("/business/", {
        retryCount: 1,
        retryDelay: 2000,
      });
      setBusinessContexts(data || []);
    } catch (err: any) {
      console.error("Error fetching business contexts:", err);
      const errorMessage = getErrorMessage(err);

      // Don't show notifications for auth-related errors or rate limiting
      const isAuthError = errorMessage.toLowerCase().includes('authentication') ||
                         errorMessage.toLowerCase().includes('unauthorized') ||
                         errorMessage.toLowerCase().includes('rate limit') ||
                         errorMessage.toLowerCase().includes('refresh rate limited') ||
                         errorMessage.toLowerCase().includes('refresh in backoff');

      if (!isAuthError) {
        setNotification({
          open: true,
          message: errorMessage,
          severity: "error",
        });
      }
    }
  };

  const fetchContentItems = async () => {
    setContentLoading(true);
    try {
      const data = await contentItemsApi.execute("/content/", {
        retryCount: 1,
        retryDelay: 2000,
      });

      if (data && Array.isArray(data.content_items)) {
        setContentItems(data.content_items);
      } else {
        setContentItems([]);
        console.error("Unexpected content items format or empty array:", data);
        setNotification({
          open: true,
          message: "Could not load content items from the library.",
          severity: "warning",
        });
      }
    } catch (err: any) {
      console.error("Error fetching content items:", err);
      const errorMessage = getErrorMessage(err);

      if (!errorMessage.toLowerCase().includes('authentication') &&
          !errorMessage.toLowerCase().includes('unauthorized') &&
          !errorMessage.toLowerCase().includes('rate limited')) {
        setNotification({
          open: true,
          message: errorMessage,
          severity: "error",
        });
      }
    } finally {
      setContentLoading(false);
    }
  };

  // Sequential loading function with proper error isolation
  const loadDataSequentially = useCallback(async () => {
    try {
      console.log("Starting sequential data loading...");

      // Load chat sessions first - critical for page function
      try {
        await fetchChatSessions();
        console.log("✅ Chat sessions loaded");
      } catch (error) {
        console.error("❌ Chat sessions failed:", error);
        // Continue with other data even if this fails
      }

      // Wait between requests to prevent auth conflicts
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load business contexts - important but not critical
      try {
        await fetchBusinessContexts();
        console.log("✅ Business contexts loaded");
      } catch (error) {
        console.error("❌ Business contexts failed:", error);
        // Continue with other data even if this fails
      }

      // Wait between requests
      await new Promise(resolve => setTimeout(resolve, 500));

      // Load content items - nice to have
      try {
        await fetchContentItems();
        console.log("✅ Content items loaded");
      } catch (error) {
        console.error("❌ Content items failed:", error);
        // This is non-critical, continue
      }

      console.log("Sequential data loading completed");
    } catch (error) {
      console.error("Error in sequential data loading:", error);
      // Don't throw - let the page function with partial data
    }
  }, [fetchChatSessions, fetchBusinessContexts, fetchContentItems]);

  // Use sequential loading to prevent simultaneous API calls that can trigger multiple 401s
  useEffect(() => {
    loadDataSequentially();
  }, [loadDataSequentially]);

  const fetchMessages = async (sessionId: string) => {
    setLoading(true);
    try {
      const response = await api.get(
        `/chat/sessions/${sessionId}`
      );
      setMessages(response.data.messages);
    } catch (err: any) {
      console.error("Error fetching messages:", err);
      setNotification({
        open: true,
        message: getErrorMessage(err.message || "Failed to fetch messages"),
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const createChatSession = async () => {
    try {
      const response = await api.post("/chat/sessions", {
        title: "New Chat",
        content_format: "general",
      });

      const newSession = response.data;
      setChatSessions([newSession, ...chatSessions]);
      setCurrentSession(newSession);
      fetchMessages(newSession.id);
    } catch (err) {
      console.error("Error creating chat session:", err);
      setNotification({
        open: true,
        message: "Failed to create chat session",
        severity: "error",
      });
    }
  };

  const deleteChatSession = async () => {
    if (!currentSession) return;

    if (!window.confirm("Are you sure you want to delete this chat session?")) {
      return;
    }

    try {
      await api.delete(`/chat/sessions/${currentSession.id}`);

      const updatedSessions = chatSessions.filter(
        (session) => session.id !== currentSession.id
      );
      setChatSessions(updatedSessions);

      if (updatedSessions.length > 0) {
        setCurrentSession(updatedSessions[0]);
        fetchMessages(updatedSessions[0].id);
      } else {
        setCurrentSession(null);
        setMessages([]);
      }

      handleMenuClose();
    } catch (err: any) {
      console.error("Error deleting chat session:", err);
      setNotification({
        open: true,
        message: getErrorMessage(err.message || "Failed to delete chat session"),
        severity: "error",
      });
    }
  };

  const applyKnowledgeSelections = useCallback(() => {
    const businessContextActive =
      includeBusinessContext && !!selectedBusinessContextId;
    const contentItemsActive = selectedContentIds.length > 0;

    setActiveKnowledgeSources({
      businessContext: businessContextActive,
      contentItems: contentItemsActive,
    });

    let message = "Knowledge sources updated: ";
    const activeSources = [];
    if (businessContextActive) activeSources.push("Business Context");
    if (contentItemsActive)
      activeSources.push(`${selectedContentIds.length} Content Items`);

    if (activeSources.length > 0) {
      message += activeSources.join(" & ");
    } else {
      message = "Knowledge sources cleared.";
    }

    setNotification({
      open: true,
      message: message,
      severity: "success",
    });
  }, [includeBusinessContext, selectedBusinessContextId, selectedContentIds]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    applyKnowledgeSelections();
  }, [includeBusinessContext, selectedContentIds, applyKnowledgeSelections]);

  const sendMessage = async () => {
    if (!messageInput.trim() || !currentSession) {
      console.warn("[CHAT] Cannot send message: missing input or session");
      return;
    }

    console.log("[CHAT] Starting message send process");
    setSending(true);
    const userInput = messageInput.trim();
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      session_id: currentSession.id,
      role: "user",
      content: userInput,
      created_at: new Date().toISOString(),
    };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setMessageInput("");

    // Add a placeholder for the assistant's response while streaming
    const assistantMessageId = `assistant-${Date.now()}`;
    const assistantPlaceholderMessage: ChatMessage = {
      id: assistantMessageId,
      session_id: currentSession.id,
      role: "assistant",
      content: "", // Start with empty content
      created_at: new Date().toISOString(),
      isLoading: true,
    };
    console.log("[CHAT] Adding assistant placeholder message:", assistantPlaceholderMessage);
    setMessages((prevMessages) => {
      const updated = [...prevMessages, assistantPlaceholderMessage];
      console.log("[CHAT] Messages after adding placeholder:", updated);
      return updated;
    });

    // Abort previous stream if any
    if (streamControllerRef.current) {
      streamControllerRef.current.abort();
    }
    streamControllerRef.current = new AbortController();

    try {
      const payload = {
        prompt: userInput,
        conversation_id: currentSession.id,
        business_context: selectedBusinessContextId,
        content_ids: selectedContentIds,
        content_format: currentSession.session_type || "general",
        client_timestamp: new Date().toISOString(),
      };

      console.log("[CHAT] Sending payload:", payload);
      console.log("[CHAT] Session token available:", !!session?.access_token);

      // Use the same base URL as the api instance
      const baseUrl = process.env.DOCKER_ENV === 'true' ? 'http://backend:5000/api' : 'http://127.0.0.1:5000/api';
      const response = await fetch(`${baseUrl}/chat/stream`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session?.access_token}`,
          "X-Chat-Request": "true",
        },
        body: JSON.stringify(payload),
        signal: streamControllerRef.current.signal,
      });

      console.log("[CHAT] Response status:", response.status);
      console.log("[CHAT] Response headers:", Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.error_message || errorMessage;
          console.error("[CHAT] Server error response:", errorData);
        } catch (parseError) {
          console.error("[CHAT] Failed to parse error response:", parseError);
          const errorText = await response.text().catch(() => "Unknown error");
          errorMessage = errorText || errorMessage;
        }

        // Handle 401 Unauthorized errors by signing out and redirecting to login
        if (response.status === 401) {
          console.log("[CHAT] 401 Unauthorized - signing out user");
          try {
            await signOut();
            navigate("/login");
            return; // Exit early to prevent further error handling
          } catch (signOutError) {
            console.error("[CHAT] Error during signOut:", signOutError);
            // Continue with normal error handling if signOut fails
          }
        }

        throw new Error(errorMessage);
      }

      if (!response.body) {
        throw new Error("Response body is null");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = "";

      // Function to process each chunk from the stream
      const processStream = async () => {
        let hasReceivedContent = false;
        let chunkCount = 0;
        let totalContentLength = 0;

        console.log("[CHAT] Starting stream processing...");

        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log(`[CHAT] Stream reading completed. Chunks: ${chunkCount}, Content length: ${totalContentLength}`);

            // If no content was received, show a fallback message
            if (!hasReceivedContent) {
              console.warn("[CHAT] No content received from stream - adding fallback message");
              setMessages((prevMessages) =>
                prevMessages.map((msg) =>
                  msg.id === assistantMessageId
                    ? {
                        ...msg,
                        content: "I'm having trouble responding right now. Please try again.",
                        isLoading: false
                      }
                    : msg
                )
              );
            } else {
              // Ensure loading state is cleared
              setMessages((prevMessages) =>
                prevMessages.map((msg) =>
                  msg.id === assistantMessageId ? { ...msg, isLoading: false } : msg
                )
              );
            }
            break;
          }

          chunkCount++;
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          console.log(`[CHAT] Chunk ${chunkCount}: ${chunk.length} chars`);

          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // Keep the last partial line in buffer

          for (const line of lines) {
            if (line.trim() === "") continue; // Skip empty lines

            console.log("[CHAT] Processing line:", line);

            if (line.startsWith("data: ")) {
              const jsonStr = line.substring(6).trim();
              console.log("[CHAT] Parsing JSON:", jsonStr);

              try {
                const eventData = JSON.parse(jsonStr);
                console.log("[CHAT] Parsed event data:", eventData);

                if (eventData.delta && typeof eventData.delta === 'string') {
                  hasReceivedContent = true;
                  totalContentLength += eventData.delta.length;
                  console.log("[CHAT] Received delta content:", eventData.delta);

                  setMessages((prevMessages) => {
                    const updated = prevMessages.map((msg) => {
                      if (msg.id === assistantMessageId) {
                        const newContent = msg.content + eventData.delta;
                        console.log(`[CHAT] Updating message content (${newContent.length} chars total)`);
                        return {
                          ...msg,
                          content: newContent,
                          isLoading: false
                        };
                      }
                      return msg;
                    });
                    return updated;
                  });
                } else if (eventData.status === "done") {
                  console.log("[CHAT] Stream finished with done status");
                  setMessages((prevMessages) =>
                    prevMessages.map((msg) =>
                      msg.id === assistantMessageId ? { ...msg, isLoading: false } : msg
                    )
                  );
                } else if (eventData.error) {
                  console.error("[CHAT] Error from stream:", eventData.error);
                  setMessages((prevMessages) =>
                    prevMessages.map((msg) =>
                      msg.id === assistantMessageId
                        ? {
                            ...msg,
                            content: msg.content || `Error: ${getErrorMessage(eventData.error)}`,
                            isLoading: false
                          }
                        : msg
                    )
                  );
                  return;
                } else {
                  console.log("[CHAT] Unknown event data structure:", eventData);
                }
              } catch (e) {
                console.error("[CHAT] Failed to parse stream data:", jsonStr, e);
                // Continue processing other lines
              }
            } else {
              console.log("[CHAT] Non-data line:", line);
            }
          }
        }
      };

      // Add timeout for streaming response
      const streamTimeout = setTimeout(() => {
        console.warn("[CHAT] Stream timeout - no response received within 30 seconds");
        setMessages((prevMessages) =>
          prevMessages.map((msg) =>
            msg.id === assistantMessageId
              ? {
                  ...msg,
                  content: msg.content || "Response timeout. Please try again.",
                  isLoading: false
                }
              : msg
          )
        );
        if (streamControllerRef.current) {
          streamControllerRef.current.abort();
        }
      }, 30000); // 30 second timeout

      try {
        await processStream();
      } finally {
        clearTimeout(streamTimeout);
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Stream aborted by user');
      } else {
        console.error("Failed to send message or process stream:", error);
        setNotification({
          open: true,
          message: getErrorMessage(error.message || "Failed to get response from assistant."),
          severity: "error",
        });
      }
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.id === assistantMessageId
            ? { ...msg, content: msg.content || "Error fetching response.", isLoading: false }
            : msg
        )
      );
    } finally {
      setSending(false);
      // Ensure isLoading is false for the specific message if not already handled
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.id === assistantMessageId && msg.isLoading ? { ...msg, isLoading: false } : msg
        )
      );
      streamControllerRef.current = null;
      clearContentSelection(); 
    }
  };

  const handleSessionSelect = (session: ChatSession) => {
    setCurrentSession(session);
    fetchMessages(session.id);
  };

  const handleMessageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessageInput(e.target.value);
  };

  const handleMessageInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
    setSessionMenuOpen(true);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSessionMenuOpen(false);
  };

  const toggleKnowledgeDrawer = () => {
    setKnowledgeDrawerOpen(!knowledgeDrawerOpen);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const extractYouTubeThumbnail = (content: string): string => {
    const thumbnailMatch = content.match(/Thumbnail:\s*([^\n]+)/);
    if (thumbnailMatch && thumbnailMatch[1]) {
      return thumbnailMatch[1].trim();
    }
    return "";
  };

  const handleSaveMessageClick = (messageToSave: ChatMessage) => {
    setMessageToSave(messageToSave);
    setIsSaveDialogOpen(true);
  };

  const handleCloseSaveDialog = () => {
    setIsSaveDialogOpen(false);
    setMessageToSave(null);
  };

  const handleSaveToLibrary = async (details: SaveContentPayload) => {
    try {
      await saveContentItem(details);

      setNotification({
        open: true,
        message: "Content saved successfully!",
        severity: "success",
      });
      handleCloseSaveDialog();
    } catch (error: any) {
      console.error("Failed to save content item:", error);
      setNotification({
        open: true,
        message: getErrorMessage(error.message || "Failed to save content. Please try again."),
        severity: "error",
      });
    }
  };



  const renderMessages = useCallback(() => {
    if (loading) {
      return (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100%",
          }}
        >
          <CircularProgress />
        </Box>
      );
    }

    if (messages.length === 0) {
      return (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
            p: 3,
            textAlign: "center",
          }}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Box
              sx={{
                borderRadius: "50%",
                bgcolor: "primary.main",
                width: 120,
                height: 120,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                mb: 3,
                boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
                border: "4px solid",
                borderColor: "background.paper",
              }}
            >
              <BotIcon sx={{ fontSize: 70, color: "#fff" }} />
            </Box>
          </motion.div>

          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Your AI Content Assistant
          </Typography>

          <Typography
            variant="body1"
            color="text.secondary"
            sx={{
              maxWidth: 500,
              mb: 3,
              lineHeight: 1.6,
            }}
          >
            Enhance your content creation with AI assistance. Select knowledge
            from your content library and let your assistant create
            personalized, context-aware content for you.
          </Typography>

          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              onClick={toggleKnowledgeDrawer}
              startIcon={<LibraryBooksIcon />}
              sx={{ borderRadius: 2 }}
            >
              Add Knowledge
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                if (messageInput.trim() === "") {
                  setMessageInput("Help me create engaging content");

                  const handleSaveMessageClickLocal = (
                    messageToSave: ChatMessage
                  ) => {
                    handleSaveMessageClick(messageToSave);
                  };
                }
                setTimeout(() => {
                  const messageField = document.getElementById("message-input");
                  if (messageField) messageField.focus();
                }, 100);
              }}
              endIcon={<SendIcon />}
              sx={{ borderRadius: 2 }}
            >
              Start Conversation
            </Button>
          </Box>
        </Box>
      );
    }

    const visibleMessages =
      messages.length > 50 ? messages.slice(messages.length - 50) : messages;

    // Debug logging for message rendering
    console.log("[CHAT] Rendering messages:", visibleMessages.map(m => ({
      id: m.id,
      role: m.role,
      content: m.content?.substring(0, 50) + '...',
      isLoading: m.isLoading
    })));

    return visibleMessages.map((message) => (
      <ChatMessageItem
        key={message.id}
        message={message}
        formatTimestamp={formatTimestamp}
        onSaveMessageClick={handleSaveMessageClick}
        setNotification={setNotification}
      />
    ));
  }, [
    messages,
    loading,
    formatTimestamp,
    handleSaveMessageClick,
    setNotification,
  ]);

  const toggleSessionSelection = (sessionId: string) => {
    setSelectedSessions((prev) => {
      if (prev.includes(sessionId)) {
        return prev.filter((id) => id !== sessionId);
      } else {
        return [...prev, sessionId];
      }
    });
  };

  const toggleSelectionMode = () => {
    setSelectionMode((prev) => !prev);
    setSelectedSessions([]);
  };

  const deleteSelectedSessions = async () => {
    if (selectedSessions.length === 0) return;

    if (
      !window.confirm(
        `Are you sure you want to delete ${selectedSessions.length} selected chat sessions?`
      )
    ) {
      return;
    }

    try {
      await api.delete("/chat/sessions/batch", {
        data: { session_ids: selectedSessions },
      });

      const updatedSessions = chatSessions.filter(
        (session) => !selectedSessions.includes(session.id)
      );
      setChatSessions(updatedSessions);

      if (currentSession && selectedSessions.includes(currentSession.id)) {
        if (updatedSessions.length > 0) {
          setCurrentSession(updatedSessions[0]);
          fetchMessages(updatedSessions[0].id);
        } else {
          setCurrentSession(null);
          setMessages([]);
        }
      }

      setNotification({
        open: true,
        message: `Successfully deleted ${selectedSessions.length} chat sessions`,
        severity: "success",
      });

      setSelectedSessions([]);
      setSelectionMode(false);
    } catch (err: any) {
      console.error("Error deleting selected chat sessions:", err);
      setNotification({
        open: true,
        message: getErrorMessage(err.message || "Failed to delete selected chat sessions"),
        severity: "error",
      });
    }
  };

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Box>
            <Typography
              variant="h4"
              fontWeight="bold"
              color="primary.main"
              gutterBottom
            >
              AI Assistant
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Generate content with AI assistance
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              variant={knowledgeDrawerOpen ? "contained" : "outlined"}
              color="primary"
              startIcon={<LibraryBooksIcon />}
              onClick={toggleKnowledgeDrawer}
              sx={{
                borderRadius: 8,
                px: 2,
                boxShadow: knowledgeDrawerOpen ? 4 : 0,
                "&:hover": {
                  boxShadow: knowledgeDrawerOpen ? 4 : 1,
                },
              }}
            >
              Knowledge Management
            </Button>
            <Button
              variant="contained"
              color="success"
              startIcon={<ChatIcon />}
              onClick={createChatSession}
              sx={{
                borderRadius: 8,
                px: 2,
                boxShadow: 4,
                "&:hover": {
                  boxShadow: 6,
                },
              }}
            >
              New Chat
            </Button>
          </Box>
        </Box>
      </motion.div>

      {sessionLoading ? (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          <Grid item sx={{ display: { xs: 'none', sm: 'block' } }} sm={4} md={3}>
            <Paper
              sx={{
                borderRadius: 2,
                height: "70vh",
                overflow: "hidden",
                display: "flex",
                flexDirection: "column",
              }}
            >
              <Box
                sx={{
                  p: 2,
                  backgroundColor: "primary.main",
                  color: "white",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography variant="h6">
                  {selectionMode
                    ? `${selectedSessions.length} Selected`
                    : "Chat Sessions"}
                </Typography>
                <Box>
                  {selectionMode && selectedSessions.length > 0 && (
                    <Tooltip title="Delete Selected Sessions">
                      <IconButton
                        onClick={deleteSelectedSessions}
                        size="small"
                        sx={{ color: "white", mr: 1 }}
                        aria-label="Delete Selected Sessions"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip
                    title={
                      selectionMode ? "Cancel Selection" : "Select Multiple"
                    }
                  >
                    <IconButton
                      onClick={toggleSelectionMode}
                      size="small"
                      sx={{ color: "white" }}
                      aria-label={selectionMode ? "Cancel Selection" : "Select Multiple"}
                    >
                      {selectionMode ? <CancelIcon /> : <EditIcon />}
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              <Divider />

              <List sx={{ overflow: "auto", flexGrow: 1 }}>
                {chatSessions.length === 0 ? (
                  <Box sx={{ p: 2, textAlign: "center" }}>
                    <Typography variant="body2" color="text.secondary">
                      No chat sessions yet
                    </Typography>
                    <Button
                      startIcon={<AddIcon />}
                      onClick={createChatSession}
                      sx={{ mt: 1 }}
                    >
                      Create New Chat
                    </Button>
                  </Box>
                ) : (
                  chatSessions.map((session) => (
                    <ListItem
                      key={session.id}
                      button
                      selected={currentSession?.id === session.id}
                      onClick={
                        selectionMode
                          ? () => toggleSessionSelection(session.id)
                          : () => handleSessionSelect(session)
                      }
                      secondaryAction={
                        selectionMode ? (
                          <Checkbox
                            edge="end"
                            checked={selectedSessions.includes(session.id)}
                            onChange={() => toggleSessionSelection(session.id)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        ) : (
                          currentSession?.id === session.id && (
                            <IconButton edge="end" onClick={handleMenuOpen} aria-label="Chat session options">
                              <MoreVertIcon />
                            </IconButton>
                          )
                        )
                      }
                    >
                      <ListItemAvatar>
                        <Avatar
                          sx={{
                            bgcolor:
                              currentSession?.id === session.id
                                ? "primary.main"
                                : "grey.400",
                          }}
                        >
                          <ChatIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={session.title}
                        secondary={new Date(
                          session.created_at
                        ).toLocaleDateString()}
                      />
                    </ListItem>
                  ))
                )}
              </List>

              <Menu
                anchorEl={menuAnchorEl}
                open={sessionMenuOpen}
                onClose={handleMenuClose}
              >
                <MenuItem onClick={deleteChatSession}>
                  <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                  Delete Chat
                </MenuItem>
                <MenuItem
                  onClick={() => {
                    handleMenuClose();
                    toggleSelectionMode();
                  }}
                >
                  <EditIcon fontSize="small" sx={{ mr: 1 }} />
                  Select Multiple
                </MenuItem>
              </Menu>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={8} md={knowledgeDrawerOpen ? 6 : 9}>
            <Paper
              sx={{
                borderRadius: 2,
                height: "70vh",
                display: "flex",
                flexDirection: "column",
              }}
            >
              {!currentSession ? (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    p: 3,
                  }}
                >
                  <ChatIcon
                    sx={{ fontSize: 60, color: "primary.main", mb: 2 }}
                  />
                  <Typography variant="h6" gutterBottom>
                    No Chat Selected
                  </Typography>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    align="center"
                    sx={{ mb: 3 }}
                  >
                    Create a new chat session or select an existing one to start
                    generating content with AI
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={createChatSession}
                  >
                    Create New Chat
                  </Button>
                </Box>
              ) : (
                <>
                  <Box
                    sx={{
                      p: 2,
                      backgroundColor: "primary.light",
                      color: "primary.contrastText",
                    }}
                  >
                    <Typography variant="h6">{currentSession.title}</Typography>
                  </Box>

                  <Divider />

                  <Box
                    ref={messageContainerRef}
                    sx={{
                      flexGrow: 1,
                      overflow: "auto",
                      p: 1.5,
                      display: "flex",
                      flexDirection: "column",
                      gap: 1.25,
                      bgcolor: (theme) =>
                        theme.palette.mode === "dark"
                          ? "rgba(0, 0, 0, 0.12)"
                          : "rgba(0, 0, 0, 0.01)",
                    }}
                  >
                    {renderMessages()}
                    <div ref={messagesEndRef} />
                  </Box>

                  <Divider />

                  {(activeKnowledgeSources.contentItems ||
                    activeKnowledgeSources.businessContext) && (
                    <Box
                      sx={{
                        px: 1.5,
                        py: 0.75,
                        bgcolor: (theme) =>
                          theme.palette.mode === "dark"
                            ? "rgba(0, 0, 0, 0.1)"
                            : "rgba(0, 0, 0, 0.02)",
                        display: "flex",
                        alignItems: "center",
                        gap: 0.5,
                        flexWrap: "wrap",
                        borderTop: "1px solid",
                        borderColor: "divider",
                      }}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <InfoIcon
                          sx={{
                            mr: 0.5,
                            color: "info.main",
                            opacity: 0.7,
                            fontSize: "0.85rem",
                          }}
                        />
                        <Typography
                          color="text.secondary"
                          sx={{ fontSize: "0.75rem", fontWeight: 500 }}
                        >
                          Using:
                        </Typography>
                      </Box>

                      <Box sx={{ display: "flex", gap: 0.5, ml: 0.5 }}>
                        {activeKnowledgeSources.contentItems && (
                          <Box
                            sx={{
                              bgcolor: "primary.main",
                              color: "primary.contrastText",
                              fontSize: "0.7rem",
                              borderRadius: 0.75,
                              py: 0.25,
                              px: 0.5,
                              display: "flex",
                              alignItems: "center",
                              gap: 0.3,
                            }}
                          >
                            <LibraryBooksIcon sx={{ fontSize: "0.75rem" }} />
                            <span>
                              {selectedContentIds.length} content items
                            </span>
                          </Box>
                        )}
                        {activeKnowledgeSources.businessContext && (
                          <Box
                            sx={{
                              bgcolor: "secondary.main",
                              color: "secondary.contrastText",
                              fontSize: "0.7rem",
                              borderRadius: 0.75,
                              py: 0.25,
                              px: 0.5,
                              display: "flex",
                              alignItems: "center",
                              gap: 0.3,
                            }}
                          >
                            <BusinessIcon sx={{ fontSize: "0.75rem" }} />
                            <span>Business context</span>
                          </Box>
                        )}
                      </Box>

                      <Button
                        size="small"
                        onClick={toggleKnowledgeDrawer}
                        sx={{
                          minWidth: 0,
                          p: 0,
                          ml: "auto",
                          fontSize: "0.75rem",
                          color: "text.secondary",
                          textTransform: "none",
                        }}
                      >
                        Edit
                      </Button>
                    </Box>
                  )}

                  <Box
                    sx={{
                      p: 1,
                      display: "flex",
                      gap: 0.75,
                      bgcolor: (theme) =>
                        theme.palette.mode === "dark"
                          ? "rgba(255, 255, 255, 0.02)"
                          : "rgba(0, 0, 0, 0.01)",
                    }}
                  >
                    <TextField
                      id="message-input"
                      label="Type your message here..."
                      fullWidth
                      multiline
                      maxRows={3}
                      placeholder="Type your message here..."
                      value={messageInput}
                      onChange={handleMessageInputChange}
                      onKeyPress={handleMessageInputKeyPress}
                      disabled={sending}
                      variant="outlined"
                      size="small"
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 1,
                          bgcolor: "background.paper",
                          boxShadow: "0 1px 1px rgba(0,0,0,0.03)",
                          fontSize: "0.85rem",
                          pt: 0.5,
                          pb: 0.5,
                        },
                        "& .MuiOutlinedInput-inputMultiline": {
                          lineHeight: 1.3,
                        },
                      }}
                    />
                    <Button
                      variant="contained"
                      color="primary"
                      endIcon={
                        sending ? undefined : <SendIcon fontSize="small" />
                      }
                      onClick={sendMessage}
                      disabled={!messageInput.trim() || sending}
                      sx={{
                        alignSelf: "flex-end",
                        borderRadius: 1,
                        px: 1.5,
                        py: 0.75,
                        boxShadow: 1,
                        minWidth: 70,
                        height: 35,
                        fontSize: "0.85rem",
                      }}
                    >
                      {sending ? <CircularProgress size={16} /> : "Send"}
                    </Button>
                  </Box>
                </>
              )}
            </Paper>
          </Grid>

          {knowledgeDrawerOpen && (
            <Grid item xs={12} md={3}>
              <Paper
                sx={{
                  borderRadius: 2,
                  height: "70vh",
                  overflow: "hidden",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <Box
                  sx={{
                    p: 2,
                    backgroundColor: "primary.main",
                    color: "white",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                  }}
                >
                  <Typography variant="h6">Knowledge Selection</Typography>
                  <IconButton
                    size="small"
                    onClick={toggleKnowledgeDrawer}
                    sx={{
                      color: "white",
                      bgcolor: "rgba(255,255,255,0.1)",
                      "&:hover": {
                        bgcolor: "rgba(255,255,255,0.2)",
                      },
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>

                <Box
                  sx={{
                    px: 2,
                    py: 1.5,
                    borderBottom: "1px solid",
                    borderColor: "divider",
                    bgcolor: (theme) =>
                      theme.palette.mode === "dark"
                        ? "rgba(255,255,255,0.03)"
                        : "rgba(0,0,0,0.01)",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
                    <BusinessIcon sx={{ mr: 1, color: "secondary.main" }} />
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      color="secondary.main"
                    >
                      Business Context
                    </Typography>
                  </Box>

                  <Card
                    variant="outlined"
                    sx={{
                      mb: 1,
                      borderRadius: 2,
                      borderColor: includeBusinessContext
                        ? "secondary.main"
                        : "divider",
                      bgcolor: includeBusinessContext
                        ? (theme) =>
                            theme.palette.mode === "dark"
                              ? "rgba(156, 39, 176, 0.08)"
                              : "rgba(156, 39, 176, 0.04)"
                        : "background.paper",
                    }}
                  >
                    <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={includeBusinessContext}
                            onChange={(e) =>
                              setIncludeBusinessContext(e.target.checked)
                            }
                            color="secondary"
                            size="small"
                          />
                        }
                        label="Include Business Context"
                        sx={{
                          mb: 0,
                          "& .MuiFormControlLabel-label": {
                            fontSize: "0.9rem",
                            fontWeight: includeBusinessContext
                              ? "bold"
                              : "normal",
                          },
                        }}
                      />

                      {includeBusinessContext && (
                        <FormControl fullWidth size="small" sx={{ mt: 1.5 }}>
                          <InputLabel id="context-select-label">
                            Select Business Context
                          </InputLabel>
                          <Select
                            labelId="context-select-label"
                            id="context-select"
                            value={selectedBusinessContextId || ""}
                            label="Select Business Context"
                            onChange={(e) =>
                              setSelectedBusinessContextId(
                                e.target.value as string | null
                              )
                            }
                            disabled={businessContexts.length === 0}
                          >
                            {businessContexts.length === 0 ? (
                              <MenuItem value="">
                                <em>No contexts available</em>
                              </MenuItem>
                            ) : (
                              businessContexts.map((context) => (
                                <MenuItem key={context.id} value={context.id}>
                                  {context.offer_description
                                    ? context.offer_description.substring(
                                        0,
                                        30
                                      ) + "..."
                                    : "Untitled Context"}
                                </MenuItem>
                              ))
                            )}
                          </Select>
                          {/* Optimize Selected Profile button removed */}
                        </FormControl>
                      )}
                    </CardContent>
                  </Card>
                </Box>

                <Box
                  sx={{
                    px: 2,
                    pt: 2,
                    pb: 1.5,
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <LibraryBooksIcon sx={{ mr: 1, color: "primary.main" }} />
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      color="primary.main"
                    >
                      Content Library
                    </Typography>
                  </Box>

                  {selectedContentIds.length > 0 && (
                    <Chip
                      label={`${selectedContentIds.length} selected`}
                      size="small"
                      color="primary"
                      sx={{ height: 24 }}
                    />
                  )}
                </Box>

                <Box
                  sx={{
                    height: "calc(100% - 180px)",
                    overflow: "auto",
                    p: 2,
                    pt: 0,
                  }}
                >
                  {contentLoading ? (
                    <Box
                      sx={{ display: "flex", justifyContent: "center", my: 4 }}
                    >
                      <CircularProgress size={30} />
                    </Box>
                  ) : contentItems.length === 0 ? (
                    <Box
                      sx={{
                        p: 3,
                        bgcolor: (theme) =>
                          theme.palette.mode === "dark"
                            ? "rgba(255, 255, 255, 0.05)"
                            : "rgba(0, 0, 0, 0.02)",
                        borderRadius: 2,
                        textAlign: "center",
                        border: "1px dashed",
                        borderColor: "divider",
                      }}
                    >
                      <LibraryBooksIcon
                        sx={{
                          fontSize: 40,
                          color: "text.secondary",
                          opacity: 0.5,
                          mb: 1,
                        }}
                      />
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        gutterBottom
                      >
                        No content items available
                      </Typography>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => navigate("/content-library")}
                        sx={{ mt: 1 }}
                        startIcon={<AddIcon />}
                      >
                        Add Content
                      </Button>
                    </Box>
                  ) : (
                    <Grid container spacing={2}>
                      {contentItems.map((item) => (
                        <Grid item xs={12} key={item.id}>
                          <Card
                            elevation={isContentSelected(item.id) ? 3 : 1}
                            sx={{
                              borderRadius: 2,
                              overflow: "hidden",
                              transition: "all 0.2s ease-in-out",
                              borderLeft: isContentSelected(item.id) ? 4 : 0,
                              borderColor: "primary.main",
                              position: "relative",
                              "&:hover": {
                                boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                                transform: "translateY(-2px)",
                              },
                            }}
                          >
                            <Box
                              onClick={() => toggleContentSelection(item.id)}
                              sx={{
                                position: "absolute",
                                top: 0,
                                right: 0,
                                m: 1,
                                width: 24,
                                height: 24,
                                borderRadius: "50%",
                                bgcolor: isContentSelected(item.id)
                                  ? "primary.main"
                                  : "background.paper",
                                border: "2px solid",
                                borderColor: isContentSelected(item.id)
                                  ? "primary.main"
                                  : "divider",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                cursor: "pointer",
                                zIndex: 2,
                                transition: "all 0.2s",
                                "&:hover": {
                                  bgcolor: isContentSelected(item.id)
                                    ? "primary.dark"
                                    : "rgba(0,0,0,0.05)",
                                },
                              }}
                            >
                              {isContentSelected(item.id) && (
                                <CheckIcon
                                  fontSize="small"
                                  sx={{ color: "white" }}
                                />
                              )}
                            </Box>

                            <Box
                              onClick={() => toggleContentSelection(item.id)}
                              sx={{ cursor: "pointer" }}
                            >
                              <Box
                                sx={{
                                  position: "absolute",
                                  top: 8,
                                  left: 8,
                                  px: 1,
                                  py: 0.5,
                                  borderRadius: 1,
                                  bgcolor: "rgba(0, 0, 0, 0.6)",
                                  color: "white",
                                  fontSize: "0.75rem",
                                  zIndex: 1,
                                }}
                              >
                                {item.content_type || "Text"}
                              </Box>

                              <Box
                                sx={{
                                  height: 100,
                                  bgcolor: (theme) =>
                                    theme.palette.mode === "dark"
                                      ? "grey.800"
                                      : "grey.100",
                                  display: "flex",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  overflow: "hidden",
                                  position: "relative",
                                }}
                              >
                                {item.content_type === "youtube_video" ? (
                                  <>
                                    {(() => {
                                      const thumbnailUrl =
                                        item.thumbnail_url ||
                                        extractYouTubeThumbnail(item.content);

                                      return thumbnailUrl ? (
                                        <Box
                                          sx={{
                                            height: "100%",
                                            width: "100%",
                                            position: "relative",
                                          }}
                                        >
                                          <img
                                            src={thumbnailUrl}
                                            alt={item.title}
                                            style={{
                                              width: "100%",
                                              height: "100%",
                                              objectFit: "cover",
                                              position: "absolute",
                                            }}
                                          />
                                          <Box
                                            sx={{
                                              position: "absolute",
                                              top: 0,
                                              left: 0,
                                              right: 0,
                                              bottom: 0,
                                              bgcolor: "rgba(0, 0, 0, 0.3)",
                                              display: "flex",
                                              alignItems: "center",
                                              justifyContent: "center",
                                            }}
                                          >
                                            <YouTubeIcon
                                              sx={{
                                                fontSize: 40,
                                                color: "#FF0000",
                                              }}
                                            />
                                          </Box>
                                        </Box>
                                      ) : (
                                        <Box
                                          sx={{
                                            position: "absolute",
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            bottom: 0,
                                            bgcolor: "grey.800",
                                            opacity: 0.6,
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                            flexDirection: "column",
                                            gap: 1,
                                          }}
                                        >
                                          <YouTubeIcon
                                            sx={{
                                              fontSize: 40,
                                              color: "#FF0000",
                                            }}
                                          />
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: "white",
                                              fontWeight: "bold",
                                            }}
                                          >
                                            YouTube Video
                                          </Typography>
                                        </Box>
                                      );
                                    })()}
                                  </>
                                ) : item.content_type === "article" ? (
                                  <>
                                    <Box
                                      sx={{
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        bottom: 0,
                                        bgcolor: "primary.dark",
                                        opacity: 0.2,
                                      }}
                                    />
                                    <Box
                                      sx={{
                                        position: "relative",
                                        zIndex: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      <ArticleIcon
                                        sx={{
                                          fontSize: 40,
                                          color: "primary.main",
                                        }}
                                      />
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "text.primary",
                                          fontWeight: "bold",
                                        }}
                                      >
                                        Article
                                      </Typography>
                                    </Box>
                                  </>
                                ) : (
                                  <>
                                    <Box
                                      sx={{
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        bottom: 0,
                                        bgcolor: "info.dark",
                                        opacity: 0.2,
                                      }}
                                    />
                                    <Box
                                      sx={{
                                        position: "relative",
                                        zIndex: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      <LibraryBooksIcon
                                        sx={{
                                          fontSize: 40,
                                          color: "info.dark",
                                        }}
                                      />
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "text.primary",
                                          fontWeight: "bold",
                                        }}
                                      >
                                        {item.content_type || "Document"}
                                      </Typography>
                                    </Box>
                                  </>
                                )}
                              </Box>

                              <CardContent sx={{ p: 1.25, pt: 1 }}>
                                <Typography
                                  variant="subtitle2"
                                  fontWeight="bold"
                                  sx={{
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    display: "-webkit-box",
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: "vertical",
                                    lineHeight: 1.3,
                                    mb: 0.5,
                                    fontSize: "0.85rem",
                                  }}
                                >
                                  {item.title}
                                </Typography>

                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  sx={{
                                    fontSize: "0.75rem",
                                    display: "-webkit-box",
                                    overflow: "hidden",
                                    WebkitBoxOrient: "vertical",
                                    WebkitLineClamp: 1,
                                    lineHeight: 1.3,
                                    mb: 1,
                                  }}
                                >
                                  {item.content && item.content.length > 80
                                    ? `${item.content.substring(0, 80)}...`
                                    : item.content}
                                </Typography>

                                <Box
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    mt: "auto",
                                  }}
                                >
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{ fontSize: "0.7rem" }}
                                  >
                                    {new Date(
                                      item.created_at
                                    ).toLocaleDateString()}
                                  </Typography>

                                  <Chip
                                    label={
                                      isContentSelected(item.id)
                                        ? "Selected"
                                        : "Select"
                                    }
                                    size="small"
                                    color={
                                      isContentSelected(item.id)
                                        ? "primary"
                                        : "default"
                                    }
                                    variant={
                                      isContentSelected(item.id)
                                        ? "filled"
                                        : "outlined"
                                    }
                                    sx={{
                                      height: 20,
                                      fontSize: "0.65rem",
                                      "& .MuiChip-label": { px: 1 },
                                    }}
                                  />
                                </Box>
                              </CardContent>
                            </Box>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  )}
                </Box>

                <Box
                  sx={{
                    p: 2,
                    display: "flex",
                    justifyContent: "space-between",
                    borderTop: "1px solid",
                    borderColor: "divider",
                    mt: "auto",
                    bgcolor: (theme) =>
                      theme.palette.mode === "dark"
                        ? "rgba(0, 0, 0, 0.2)"
                        : "rgba(0, 0, 0, 0.02)",
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={() => clearContentSelection()}
                    size="small"
                    disabled={selectedContentIds.length === 0}
                    startIcon={
                      selectedContentIds.length > 0 ? (
                        <DeleteIcon fontSize="small" />
                      ) : undefined
                    }
                  >
                    {selectedContentIds.length > 0
                      ? `Clear (${selectedContentIds.length})`
                      : "Clear"}
                  </Button>
                </Box>
              </Paper>
            </Grid>
          )}
        </Grid>
      )}

      <SaveToLibraryDialog
        open={isSaveDialogOpen}
        onClose={handleCloseSaveDialog}
        onSave={handleSaveToLibrary}
        contentPreview={messageToSave?.content || ""}
        initialTitle={
          messageToSave?.content?.split("\n")[0]?.substring(0, 80) || ""
        }
      />

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, open: false })}
          severity={notification.severity}
          sx={{ width: "100%" }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Profile optimization notification snackbars removed */}
    </Box>
  );
};

const AIAssistantPage = () => (
  <AuthErrorBoundary onRetry={() => window.location.reload()}>
    <ContentSelectionProvider>
      <AIAssistant />
    </ContentSelectionProvider>
  </AuthErrorBoundary>
);

export default AIAssistantPage;
