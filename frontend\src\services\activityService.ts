import { api } from "./api";

export interface UserActivity {
  id: number;
  userId: number;
  activityType: "chat" | "search" | "content";
  title: string;
  description?: string;
  resourceId?: string;
  resourceType?: string;
  linkPath?: string;
  createdAt: string;
  timeAgo: string;
}

export interface ActivityResponse {
  success: boolean;
  activities: UserActivity[];
  count: number;
}

export interface LogActivityRequest {
  activityType: string;
  title: string;
  description?: string;
  resourceId?: string;
  resourceType?: string;
  linkPath?: string;
}

/**
 * Get recent user activities
 * @param limit Number of activities to fetch (default: 10)
 * @returns Promise with user activities
 */
export const getUserActivities = async (
  limit: number = 10
): Promise<ActivityResponse> => {
  try {
    const response = await api.get(`/api/activity/?limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching user activities:", error);
    throw error;
  }
};

/**
 * Log a new user activity
 * @param activity Activity data to log
 * @returns Promise with the created activity
 */
export const logUserActivity = async (
  activity: LogActivityRequest
): Promise<{ success: boolean; activity: UserActivity }> => {
  try {
    const response = await api.post("/api/activity/", activity);
    return response.data;
  } catch (error) {
    console.error("Error logging user activity:", error);

    // Fail silently in production - activity logging should not break the app flow
    if (process.env.NODE_ENV === "production") {
      return { success: false, activity: null as any };
    }

    throw error;
  }
};

/**
 * Helper function to track AI assistant activity
 */
export const trackChatActivity = async (
  sessionId: string,
  title: string,
  content: string
) => {
  try {
    return await logUserActivity({
      activityType: "chat",
      title: title || "AI Assistant Chat",
      description:
        content.substring(0, 100) + (content.length > 100 ? "..." : ""),
      resourceId: sessionId,
      resourceType: "chatSession",
      linkPath: `/ai-assistant/sessions/${sessionId}`,
    });
  } catch (error) {
    console.error("Error tracking chat activity:", error);
    return { success: false, activity: null as any };
  }
};

/**
 * Helper function to track YouTube search activity
 */
export const trackSearchActivity = async (searchId: string, query: string) => {
  try {
    return await logUserActivity({
      activityType: "search",
      title: "YouTube Search",
      description: `Search for: ${query}`,
      resourceId: searchId,
      resourceType: "search",
      linkPath: `/youtube-analytics?q=${encodeURIComponent(query)}`,
    });
  } catch (error) {
    console.error("Error tracking search activity:", error);
    return { success: false, activity: null as any };
  }
};

/**
 * Helper function to track content library activity
 */
export const trackContentActivity = async (
  contentId: string,
  title: string,
  contentType: string
) => {
  try {
    return await logUserActivity({
      activityType: "content",
      title: `${contentType} Update`,
      description: title,
      resourceId: contentId,
      resourceType: "content",
      linkPath: `/content-library/content/${contentId}`,
    });
  } catch (error) {
    console.error("Error tracking content activity:", error);
    return { success: false, activity: null as any };
  }
};
