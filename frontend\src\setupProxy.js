const { createProxyMiddleware } = require("http-proxy-middleware");

module.exports = function (app) {
  // Determine if running in Docker and set the backend URL accordingly
  console.log("Environment variables:", {
    NODE_ENV: process.env.NODE_ENV,
    DOCKER_ENV: process.env.DOCKER_ENV,
    REACT_APP_API_URL: process.env.REACT_APP_API_URL,
  });

  // Enhanced Docker environment detection
  const isDocker = process.env.DOCKER_ENV === "true";
  const nodeEnv = process.env.NODE_ENV;
  const reactAppApiUrl = process.env.REACT_APP_API_URL;

  console.log("Environment detection:", {
    isDocker,
    nodeEnv,
    reactAppApiUrl,
    dockerEnvVar: process.env.DOCKER_ENV
  });

  // Determine backend URL based on environment with fallback logic
  let backendUrl;

  // Determine backend URL based on environment
  if (isDocker) {
    console.log("🐳 Docker environment detected - using Docker service name");
    backendUrl = "http://backend:5000";
  } else {
    console.log("💻 Local development environment - using localhost");
    backendUrl = "http://127.0.0.1:5000";
  }

  console.log(`🔗 Proxy configured for backend at: ${backendUrl}`);
  console.log(`Proxy configured to use backend at: ${backendUrl}`);

  // Create the API proxy middleware with enhanced configuration
  const apiProxy = createProxyMiddleware({
    target: backendUrl,
    changeOrigin: true,
    secure: false,
    // Remove pathRewrite since backend routes already include /api prefix
    // Add timeout and retry configuration
    timeout: 10000, // 10 second timeout (reduced from 30)
    proxyTimeout: 10000,
    // Add headers for better compatibility
    headers: {
      'Connection': 'keep-alive',
    },
    onProxyReq: (proxyReq, req, res) => {
      // Log proxy requests for debugging (without sensitive data)
      console.log(`Proxying ${req.method} ${req.url} to ${proxyReq.path}`);
      
      // Only log non-sensitive headers count for debugging
      const headerCount = Object.keys(req.headers).length;
      console.log(`Request has ${headerCount} headers`);

      // Log content type for debugging (safe to log)
      if (req.headers['content-type']) {
        console.log(`Content-Type: ${req.headers['content-type']}`);
      }

      // REMOVED: Authorization header logging to prevent secret leakage
      // REMOVED: Request body logging to prevent sensitive data exposure
    },
    onProxyRes: (proxyRes, req, res) => {
      // Log response status for debugging
      console.log(`Response from ${req.url}: ${proxyRes.statusCode}`);

      // If there's an error (4xx or 5xx), log safe details only
      if (proxyRes.statusCode >= 400) {
        // Only log safe response headers (not Authorization or sensitive data)
        const safeHeaders = ['content-type', 'content-length'];
        const logHeaders = {};
        safeHeaders.forEach(header => {
          if (proxyRes.headers[header]) {
            logHeaders[header] = proxyRes.headers[header];
          }
        });
        console.log("Safe response headers:", logHeaders);

        // For 422 errors, log more details
        if (proxyRes.statusCode === 422) {
          console.log("422 Unprocessable Entity error detected");
          console.log(
            "This usually indicates a validation error or JWT token issue"
          );
        }
      }
    },
    onError: (err, req, res) => {
      console.error("Proxy error:", err.message); // Only log error message, not full error with stack
      console.error("Backend URL:", backendUrl);
      console.error("Docker environment:", process.env.DOCKER_ENV);
      console.error("Node environment:", process.env.NODE_ENV);

      res.writeHead(500, {
        "Content-Type": "application/json",
      });
      res.end(JSON.stringify({
        error: "Proxy Error",
        message: `Could not connect to backend server at ${backendUrl}`,
        details: err.message, // Only expose error message, not sensitive details
        environment: {
          docker: process.env.DOCKER_ENV,
          node: process.env.NODE_ENV,
          backendUrl: backendUrl
        },
        timestamp: new Date().toISOString()
      }));
    },
  });

  // Apply the API proxy middleware - ONLY for /api routes
  console.log("🔧 Setting up proxy: /api -> " + backendUrl);
  app.use("/api", apiProxy);

  // Create a separate proxy for webpack hot module replacement
  // This ensures HMR requests don't try to go to the backend
  app.use("/*.hot-update.json", (req, res, next) => {
    console.log("Intercepted HMR request:", req.url);
    next();
  });

  // Add a test endpoint to verify proxy is working
  app.use("/proxy-test", (req, res) => {
    res.json({
      message: "Proxy is configured correctly",
      backendUrl: backendUrl,
      environment: {
        isDocker: isDocker,
        nodeEnv: nodeEnv,
        dockerEnv: process.env.DOCKER_ENV
      }
    });
  });
};
